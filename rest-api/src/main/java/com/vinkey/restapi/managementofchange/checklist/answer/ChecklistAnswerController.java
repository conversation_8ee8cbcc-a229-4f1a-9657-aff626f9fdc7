package com.vinkey.restapi.managementofchange.checklist.answer;

import com.vinkey.restapi.common.file.FileService;
import com.vinkey.restapi.common.persistence.Deletable;
import com.vinkey.restapi.common.persistence.PaginatedResult;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import com.vinkey.restapi.managementofchange.change.Change;
import com.vinkey.restapi.managementofchange.change.ChangeService;
import com.vinkey.restapi.managementofchange.checklist.answer.dto.AnswerChange;
import com.vinkey.restapi.managementofchange.checklist.answer.dto.AnswerCreate;
import com.vinkey.restapi.managementofchange.checklist.answer.dto.AnswerRead;
import com.vinkey.restapi.managementofchange.checklist.answer.dto.AnswerUpdate;
import com.vinkey.restapi.managementofchange.checklist.answer.exception.ChecklistAnswerNotFoundException;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/checklists/templates/answers")
public class ChecklistAnswerController {
  private final ChecklistAnswerService checklistAnswerService;
  private final ChecklistAnswerMapper checklistAnswerMapper;
  private final ChecklistAnswerHistoryService checklistAnswerHistoryService;
  private final FileService fileService;
  private final ChangeService changeService;

  public ChecklistAnswerController(
      ChecklistAnswerService checklistAnswerService,
      ChecklistAnswerHistoryService checklistAnswerHistoryService,
      ChangeService changeService,
      FileService fileService) {
    this.checklistAnswerService = checklistAnswerService;
    this.checklistAnswerMapper = ChecklistAnswerMapper.INSTANCE;
    this.checklistAnswerHistoryService = checklistAnswerHistoryService;
    this.changeService = changeService;
    this.fileService = fileService;
  }

  @GetMapping
  @ResponseStatus(HttpStatus.OK)
  public PaginatedResult<AnswerRead> getChecklistAnswers(
      @RequestParam(required = false) IssueStatus status,
      @RequestParam(required = false) List<String> candidateGroups,
      @RequestParam(required = false) List<Long> candidateUsers,
      @RequestParam(required = false) Long createdBy,
      @RequestParam(required = false) Long groupId,
      @RequestParam(required = false) Long ancestorId,
      @RequestParam(required = false) Long changeId,
      @RequestParam(required = false) String filter,
      @RequestParam(required = false) String search,
      @RequestParam(required = false) String doneBefore,
      @RequestParam(required = false) Long assignee,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) Long pageNumber,
      @RequestParam(required = false) Long pageSize,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    Page<ChecklistAnswer> answers =
        checklistAnswerService.readAll(
            jwtDetails.getTenantId(),
            status,
            candidateGroups,
            candidateUsers,
            createdBy,
            groupId,
            ancestorId,
            changeId,
            filter,
            search,
            doneBefore,
            assignee,
            sort,
            pageNumber,
            pageSize);
    return checklistAnswerMapper.answerToAnswerRead(answers);
  }

  @GetMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public AnswerRead getChecklistAnswer(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    ChecklistAnswer checklistAnswer =
        this.checklistAnswerService.read(id, jwtDetails.getTenantId());

    if (checklistAnswer.getAnswer() != Answer.YES) {
      throw new ChecklistAnswerNotFoundException();
    }

    checklistAnswer.setFiles(
        fileService.setUrls(checklistAnswer.getFiles().stream().toList()).stream()
            .collect(Collectors.toSet()));

    return checklistAnswerMapper.answerToAnswerRead(checklistAnswer);
  }

  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public AnswerRead createChecklistAnswer(
      @Valid @RequestBody AnswerCreate checklistAnswerCreate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    Change change = changeService.read(checklistAnswerCreate.getChange(), jwtDetails.getTenantId());
    ChecklistAnswer checklistAnswer =
        checklistAnswerMapper.answerCreateToAnswer(
            checklistAnswerCreate, jwtDetails.getTenantId(), change.getGroup().getId());
    ChecklistAnswer referenceAnswer = checklistAnswerService.create(checklistAnswer);
    ChecklistAnswer createdChecklistAnswer =
        checklistAnswerService.read(referenceAnswer.getId(), jwtDetails.getTenantId());
    return checklistAnswerMapper.answerToAnswerRead(createdChecklistAnswer);
  }

  @GetMapping("/{id}/deletable")
  @ResponseStatus(HttpStatus.OK)
  public Deletable getDeletableChecklistAnswer(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    ChecklistAnswer checklistAnswer = checklistAnswerService.read(id, jwtDetails.getTenantId());
    Boolean deletable = checklistAnswerService.checkDeletable(checklistAnswer);
    Deletable checklistAnswerDeletable = new Deletable();
    checklistAnswerDeletable.setDeletable(deletable);
    return checklistAnswerDeletable;
  }

  @PutMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public AnswerRead updateChecklistAnswer(
      @PathVariable Long id,
      @Valid @RequestBody AnswerUpdate checklistAnswerUpdate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    ChecklistAnswer checklistAnswer = checklistAnswerService.read(id, jwtDetails.getTenantId());
    checklistAnswerMapper.updateAnswerFromAnswerUpdate(checklistAnswer, checklistAnswerUpdate);
    Long updatedChecklistAnswerId = checklistAnswerService.update(checklistAnswer);
    ChecklistAnswer updatedChecklistAnswer =
        checklistAnswerService.read(updatedChecklistAnswerId, jwtDetails.getTenantId());
    return checklistAnswerMapper.answerToAnswerRead(updatedChecklistAnswer);
  }

  @DeleteMapping("/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteChecklistAnswer(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    ChecklistAnswer checklistAnswer = checklistAnswerService.read(id, jwtDetails.getTenantId());
    checklistAnswerService.delete(checklistAnswer);
  }

  @GetMapping(value = "/{id}/history")
  @ResponseBody
  public List<AnswerChange> getChecklistAnswerHistory(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    return checklistAnswerHistoryService.getHistory(id, jwtDetails.getTenantId());
  }

  @GetMapping(value = "/{id}/pdf", produces = MediaType.APPLICATION_PDF_VALUE)
  @ResponseBody
  public void getIssuePdf(
      @PathVariable Long id,
      @RequestParam(required = false) String timeZone,
      @AuthenticationPrincipal JwtDetails jwtDetails,
      HttpServletResponse response)
      throws IOException {
    ChecklistAnswer checklistAnswer = checklistAnswerService.read(id, jwtDetails.getTenantId());
    response.setContentType(MediaType.APPLICATION_PDF_VALUE);
    response.addHeader(
        HttpHeaders.CONTENT_DISPOSITION,
        "attachment; filename=Issue " + checklistAnswer.getSid() + ".pdf");
    checklistAnswerService.generateIssuePdf(checklistAnswer, timeZone, response.getOutputStream());
  }
}
