package com.vinkey.restapi.managementofchange.checklist.answer;

import com.vinkey.restapi.common.persistence.sort.SortByEnum;
import com.vinkey.restapi.identityandaccess.group.Group_;
import com.vinkey.restapi.identityandaccess.user.User_;
import com.vinkey.restapi.managementofchange.change.Change_;
import com.vinkey.restapi.managementofchange.checklist.Checklist_;
import com.vinkey.restapi.managementofchange.checklist.question.ChecklistQuestion_;
import com.vinkey.restapi.managementofchange.checklist.template.ChecklistTemplate_;

public enum ChecklistAnswerSortBy implements SortByEnum {
  ID(ChecklistAnswer_.ID),
  SID(ChecklistAnswer_.SID),
  DESCRIPTION(ChecklistAnswer_.DESCRIPTION),
  STATUS(ChecklistAnswer_.STATUS),
  DONE_BEFORE(ChecklistAnswer_.DONE_BEFORE),
  CREATED_AT(ChecklistAnswer_.CREATION_DATE),
  MODIFIED_AT(ChecklistAnswer_.MODIFIED_DATE),
  GROUP_NAME(ChecklistAnswer_.GROUP + "." + Group_.NAME),
  ASSIGNEE_NAME(ChecklistAnswer_.ASSIGNEE + "." + User_.FULL_NAME),
  CHANGE_SID(ChecklistAnswer_.CHANGE + "." + Change_.SID),
  CHANGE_TITLE(ChecklistAnswer_.CHANGE + "." + Change_.TITLE),
  CHECKLIST_TITLE(ChecklistAnswer_.CHECKLIST + "." + Checklist_.CHECKLIST_TEMPLATE + "." + ChecklistTemplate_.NAME),
  QUESTION_NAME(ChecklistAnswer_.QUESTION + "." + ChecklistQuestion_.NAME);

  private final String field;

  ChecklistAnswerSortBy(String field) {
    this.field = field;
  }

  @Override
  public String getField() {
    return field;
  }
}
