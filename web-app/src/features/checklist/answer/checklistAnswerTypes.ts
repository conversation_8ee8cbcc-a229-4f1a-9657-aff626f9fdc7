import { SvgIconPropsColorOverrides } from '@mui/material';
import { OverridableStringUnion } from '@mui/types';
import { ChangeDisplay } from '../../change/changeTypes';
import { GroupDisplay, GroupListRead } from '../../group/groupTypes';
import { User, UserDisplay } from '../../user/userTypes';
import {
  ChecklistDoneBefore,
  ChecklistQuestionRead,
  ChecklistTemplateDisplay,
} from '../template/checklistTemplateTypes';
import { FileDisplay } from '../../file/fileTypes';
import { themeToColor } from '../../../theme';
import { EnumMetaItem, EnumMetaMap } from '../../../types';
import { createDynamicMetaMap, getWeekNumberAndYear, sortDates } from '../../../utils';

export interface ChecklistAnswerParams {
  status?: IssueStatus;
  candidateGroups?: ChecklistAnswerCandidateGroups[];
  candidateUsers?: number[];
  createdBy?: number;
  groupId?: number;
  ancestorId?: number;
  changeId?: number;
  search?: string;
  filter?: string;
  sort?: string;
  pageNumber?: number;
  pageSize?: number;
  doneBefore?: ChecklistDoneBefore;
  assignee?: number;
}

export interface ChecklistAnswerPDFParams {
  id: number;
  timeZone: string;
}

export interface ChecklistDisplay {
  id: number;
  sid: number;
  checklistTemplate: ChecklistTemplateDisplay;
  change: ChangeDisplay;
}

export interface ChecklistAnswerRead {
  id: number;
  sid?: number;
  group: GroupDisplay;
  processInstanceId: string;
  checklist: ChecklistDisplay;
  question: ChecklistQuestionRead;
  doneBefore: ChecklistDoneBefore;
  answer: Answer;
  description?: string;
  files: FileDisplay[];
  locked: boolean;
  status: IssueStatus;
  createdBy: UserDisplay;
  creationDate: number;
  modifiedBy: UserDisplay;
  modifiedDate: number;
  assignee: UserDisplay;
  change: ChangeDisplay;
}

export interface ChecklistAnswerCreate {
  description: string;
  files: number[];
  doneBefore: ChecklistDoneBefore;
  change: number;
  assignee?: number;
  answer?: Answer;
}

export interface ChecklistAnswerUpdate {
  id: number;
  description: string;
  files: number[];
  doneBefore: ChecklistDoneBefore;
  assignee?: number;
}

export interface ChecklistAnswerFormInput {
  description: string;
  files: FileDisplay[];
  doneBefore: ChecklistDoneBefore | null;
  assignee: UserDisplay | null;
  answer?: Answer;
}

export enum Answer {
  NO = 'NO',
  YES = 'YES',
  NA = 'NA',
}

export const AnswerDisplayMap: Record<Answer, string> = {
  NO: 'No',
  YES: 'Yes',
  NA: 'Not applicable',
};

export const AnswerColorMap: Record<
  Answer,
  OverridableStringUnion<
    'inherit' | 'action' | 'disabled' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning',
    SvgIconPropsColorOverrides
  >
> = {
  NO: 'primary',
  YES: 'error',
  NA: 'disabled',
};

export enum IssueStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  RESOLVED = 'RESOLVED',
  CANCELED = 'CANCELED',
}

export const IssueStatusDisplayMap: Record<IssueStatus, string> = {
  DRAFT: 'Draft',
  ACTIVE: 'Active',
  RESOLVED: 'Resolved',
  CANCELED: 'Canceled',
};

export interface ChecklistAnswerViewState {
  listView: 'open' | 'all';
  group?: GroupListRead;
  status?: IssueStatus;
  createdBy?: User;
  assignedUser?: User;
  candidateGroups?: ChecklistAnswerCandidateGroups[];
  search?: string;
  doneBefore?: ChecklistDoneBefore;
  assignee?: User;
  changeId?: number;
  columns?: ChecklistAnswerColumnSetting[];
  sort?: ChecklistAnswerSort[];
}

export interface ChecklistAnswerState {
  checklistAnswerViewState: ChecklistAnswerViewState;
  checklistAnswerTabViewState: ChecklistAnswerViewState;
}

export enum ChecklistAnswerCandidateGroups {
  ISSUE_RESOLVE = 'ISSUE_RESOLVE',
}

export const ChecklistAnswerCandidateGroupsDisplayMap: Record<ChecklistAnswerCandidateGroups, string> = {
  ISSUE_RESOLVE: 'Resolve',
};

export interface ChecklistAnswerChange {
  by: UserDisplay;
  at: number;
  type: ChecklistAnswerChangeType;
  oldEntity: ChecklistAnswerRead;
  newEntity: ChecklistAnswerRead;
}

export enum ChecklistAnswerChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const ChecklistAnswerChangeTypeDisplayMap: Record<ChecklistAnswerChangeType, string> = {
  INSERT: 'Issue created',
  UPDATE: 'Issue updated',
  DELETE: 'Issue deleted',
};

export enum ChecklistAnswerGroupBy {
  GROUP = 'CHECKLIST_ANSWERS_GROUP',
  START_DATE_WEEK = 'CHECKLIST_ANSWERS_START_DATE_WEEK',
}

export const ChecklistAnswerGroupByDisplayMap: Record<ChecklistAnswerGroupBy, string> = {
  [ChecklistAnswerGroupBy.GROUP]: 'Group',
  [ChecklistAnswerGroupBy.START_DATE_WEEK]: 'Checklist answer week',
};

export const CreationDateWeekMeta = createDynamicMetaMap<string, EnumMetaItem>((iso) => ({
  label: getWeekNumberAndYear(iso),
  color: themeToColor('primary.main'),
}));

export interface ChecklistAnswerGroupByFieldType {
  [ChecklistAnswerGroupBy.START_DATE_WEEK]: string; // ISO date strings
}

export const ChecklistAnswerGroupByFieldMetaMap: {
  [K in keyof ChecklistAnswerGroupByFieldType]: EnumMetaMap<ChecklistAnswerGroupByFieldType[K]>;
} = {
  [ChecklistAnswerGroupBy.START_DATE_WEEK]: CreationDateWeekMeta,
};

export const ChecklistAnswerGroupByFieldSortFunctionMap = {
  [ChecklistAnswerGroupBy.START_DATE_WEEK]: sortDates,
};

export enum ChecklistAnswerSortField {
  SID = 'sid',
  DESCRIPTION = 'description',
  STATUS = 'status',
  DONE_BEFORE = 'done_before',
  CREATED_AT = 'created_at',
  MODIFIED_AT = 'modified_at',
  GROUP_NAME = 'group_name',
  ASSIGNEE_NAME = 'assignee_name',
  CHANGE_SID = 'change_sid',
  CHANGE_TITLE = 'change_title',
  CHECKLIST_TITLE = 'checklist_title',
  QUESTION_NAME = 'question_name',
}

export const ChecklistAnswerFieldSortMap: Partial<Record<keyof ChecklistAnswerRead, ChecklistAnswerSortField>> = {
  sid: ChecklistAnswerSortField.SID,
  description: ChecklistAnswerSortField.DESCRIPTION,
  status: ChecklistAnswerSortField.STATUS,
  doneBefore: ChecklistAnswerSortField.DONE_BEFORE,
  creationDate: ChecklistAnswerSortField.CREATED_AT,
  modifiedDate: ChecklistAnswerSortField.MODIFIED_AT,
};

// Additional mapping for virtual fields that don't exist directly on ChecklistAnswerRead
export const ChecklistAnswerVirtualFieldSortMap: Record<string, ChecklistAnswerSortField> = {
  group: ChecklistAnswerSortField.GROUP_NAME,
  assignee: ChecklistAnswerSortField.ASSIGNEE_NAME,
  change: ChecklistAnswerSortField.CHANGE_SID,
  checklist: ChecklistAnswerSortField.CHECKLIST_TITLE,
  question: ChecklistAnswerSortField.QUESTION_NAME,
};

export interface ChecklistAnswerSort {
  field: ChecklistAnswerSortField;
  direction: 'asc' | 'desc';
}

export enum ChecklistAnswerColumn {
  SID = 'sid',
  DESCRIPTION = 'description',
  GROUP = 'group',
  ASSIGNEE = 'assignee',
  CHECKLIST = 'checklist',
  CHANGE = 'change',
  DONE_BEFORE = 'doneBefore',
  STATUS = 'status',
  DATE = 'date',
}

export const ChecklistAnswerColumnDisplayMap: Record<ChecklistAnswerColumn, string> = {
  [ChecklistAnswerColumn.SID]: 'ID',
  [ChecklistAnswerColumn.DESCRIPTION]: 'Description',
  [ChecklistAnswerColumn.GROUP]: 'Group',
  [ChecklistAnswerColumn.ASSIGNEE]: 'Assignee',
  [ChecklistAnswerColumn.CHECKLIST]: 'Checklist',
  [ChecklistAnswerColumn.CHANGE]: 'Change',
  [ChecklistAnswerColumn.DONE_BEFORE]: 'Completion phase',
  [ChecklistAnswerColumn.STATUS]: 'Status',
  [ChecklistAnswerColumn.DATE]: 'Date',
};

export interface ChecklistAnswerColumnSetting {
  column: ChecklistAnswerColumn;
  hidden: boolean;
  width: number;
}

export const ChecklistAnswerColumnDefaults: ChecklistAnswerColumnSetting[] = [
  {
    column: ChecklistAnswerColumn.SID,
    hidden: false,
    width: 75,
  },
  {
    column: ChecklistAnswerColumn.DESCRIPTION,
    hidden: false,
    width: 400,
  },
  {
    column: ChecklistAnswerColumn.GROUP,
    hidden: false,
    width: 200,
  },
  {
    column: ChecklistAnswerColumn.ASSIGNEE,
    hidden: false,
    width: 200,
  },
  {
    column: ChecklistAnswerColumn.CHECKLIST,
    hidden: false,
    width: 250,
  },
  {
    column: ChecklistAnswerColumn.CHANGE,
    hidden: false,
    width: 250,
  },
  {
    column: ChecklistAnswerColumn.DONE_BEFORE,
    hidden: false,
    width: 150,
  },
  {
    column: ChecklistAnswerColumn.STATUS,
    hidden: false,
    width: 130,
  },
  {
    column: ChecklistAnswerColumn.DATE,
    hidden: false,
    width: 178,
  },
];
