import { <PERSON>, Stack, I<PERSON><PERSON><PERSON>on, Pop<PERSON>, Typography, FormControlLabel, Checkbox } from '@mui/material';
import ViewColumnIcon from '@mui/icons-material/ViewColumn';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import { useState } from 'react';
import {
  ChecklistAnswerCandidateGroupsDisplayMap,
  ChecklistAnswerCandidateGroups,
  ChecklistAnswerViewState,
  ChecklistAnswerColumn,
  ChecklistAnswerColumnDefaults,
  ChecklistAnswerColumnDisplayMap,
  ChecklistAnswerColumnSetting,
} from './checklistAnswerTypes';
import { User } from '../../user/userTypes';

interface ChecklistAnswerChipFilterProps {
  me?: User;
  resetPageNumber: () => void;
  value: ChecklistAnswerViewState;
  onChange: (newValue: ChecklistAnswerViewState) => void;
}

function ChecklistAnswerChipFilter({ me, resetPageNumber, value, onChange }: ChecklistAnswerChipFilterProps) {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [columnsOrder, setColumnsOrder] = useState<ChecklistAnswerColumnSetting[]>(
    value.columns || ChecklistAnswerColumnDefaults
  );
  const [draggingIndex, setDraggingIndex] = useState<number | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  const handleDragStart = (index: number) => {
    setDraggingIndex(index);
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (index: number) => {
    if (draggingIndex !== null && draggingIndex !== index) {
      const newOrder = [...columnsOrder];
      const [moved] = newOrder.splice(draggingIndex, 1);
      newOrder.splice(index, 0, moved);
      setColumnsOrder(newOrder);
      // Update view state with the new ordering.
      onChange({ ...value, columns: newOrder });
    }
    setDraggingIndex(null);
  };

  const handleToggleColumn = (column: ChecklistAnswerColumn) => {
    // Toggle the hidden flag for the selected column.
    const newOrder = columnsOrder.map((setting) => {
      if (setting.column === column) {
        return { ...setting, hidden: !setting.hidden };
      }
      return setting;
    });
    setColumnsOrder(newOrder);
    onChange({ ...value, columns: newOrder });
  };

  return (
    <Stack direction="row" flexWrap={{ xs: 'nowrap', sm: 'wrap' }} overflow={{ xs: 'scroll', sm: 'unset' }}>
      <IconButton size="small" onClick={handleClick} sx={{ mb: 1, mr: 1 }}>
        <ViewColumnIcon fontSize="small" />
      </IconButton>
      <Chip
        label="Created by me"
        sx={{ mb: 1, mr: 1 }}
        color={value.createdBy ? 'primary' : 'default'}
        onClick={() => {
          const newState = { ...value };
          if (newState.createdBy) {
            newState.createdBy = undefined;
          } else {
            newState.createdBy = me;
          }
          onChange(newState);
          resetPageNumber();
        }}
      />
      <Chip
        label="Assigned to me"
        sx={{ mb: 1, mr: 1 }}
        color={value.assignee ? 'primary' : 'default'}
        onClick={() => {
          const newState = { ...value };
          if (newState.assignee) {
            newState.assignee = undefined;
          } else {
            newState.assignee = me;
          }
          onChange(newState);
          resetPageNumber();
        }}
      />
      {Object.values(ChecklistAnswerCandidateGroups).map((g) => (
        <Chip
          sx={{ mb: 1, mr: 1 }}
          label={ChecklistAnswerCandidateGroupsDisplayMap[g]}
          color={value.candidateGroups?.find((c) => c === g) ? 'primary' : 'default'}
          onClick={() => {
            const newState = { ...value };
            if (!!newState.candidateGroups && !!newState.candidateGroups.find((c) => c === g)) {
              newState.candidateGroups = newState.candidateGroups.filter((c) => c !== g);
            } else {
              const newGroups = newState.candidateGroups || [];
              newState.candidateGroups = newGroups.concat(g);
            }
            onChange(newState);
            resetPageNumber();
          }}
        />
      ))}
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        <Stack sx={{ p: 2, width: 300 }}>
          <Typography variant="subtitle1">Columns</Typography>
          <Stack>
            {columnsOrder.map((setting, index) => (
              <div
                key={setting.column}
                draggable
                onDragStart={() => handleDragStart(index)}
                onDragOver={handleDragOver}
                onDrop={() => handleDrop(index)}
                style={{ display: 'flex', alignItems: 'center', cursor: 'grab' }}
              >
                <DragIndicatorIcon fontSize="small" style={{ marginRight: 8 }} />
                <FormControlLabel
                  control={<Checkbox checked={!setting.hidden} onChange={() => handleToggleColumn(setting.column)} />}
                  label={ChecklistAnswerColumnDisplayMap[setting.column]}
                />
              </div>
            ))}
          </Stack>
        </Stack>
      </Popover>
    </Stack>
  );
}

export default ChecklistAnswerChipFilter;
