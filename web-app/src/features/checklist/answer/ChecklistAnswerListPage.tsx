import { Tab<PERSON>ontext, TabPanel } from '@mui/lab';
import { Box, Paper, Tab, Tabs } from '@mui/material';
import { DataGrid, GridColDef, GridPaginationModel, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';
import { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useGetChecklistAnswersQuery } from './checklistAnswerApi';
import {
  ChecklistAnswerColumn,
  ChecklistAnswerColumnDefaults,
  ChecklistAnswerColumnDisplayMap,
  ChecklistAnswerFieldSortMap,
  ChecklistAnswerVirtualFieldSortMap,
  ChecklistAnswerParams,
  ChecklistAnswerRead,
  ChecklistAnswerSort,
  ChecklistAnswerSortField,
  IssueStatus,
  IssueStatusDisplayMap,
} from './checklistAnswerTypes';
import ErrorGate from '../../../components/ErrorGate';
import PageTitle from '../../title/Title';
import ChecklistAnswerFilterBar from './ChecklistAnswerFilterBar';
import ChecklistAnswerChipFilter from './ChecklistAnswerChipFilter';
import { useGetCurrentUserQuery } from '../../user/userApi';
import { useAppDispatch, useAppSelector } from '../../../store';
import { setChecklistAnswerViewState } from './checklistAnswerSlice';
import { ChecklistDoneBeforeDisplayMap } from '../template/checklistTemplateTypes';
import { DataGridCellLinkWrapper } from '../../../components/DataGridCellLink';
import GroupCell from '../../../components/GroupCell';
import SidCell from '../../../components/SidCell';
import UserCell from '../../../components/UserCell';
import ChecklistAnswerSidCell from './cell/ChecklistAnswerSidCell';
import ChecklistAnswerDescriptionCell from './cell/ChecklistAnswerDescriptionCell';
import ChecklistAnswerStatusCell from './cell/ChecklistAnswerStatusCell';
import ChecklistAnswerChangeCell from './cell/ChecklistAnswerChangeCell';
import ChecklistAnswerChecklistCell from './cell/ChecklistAnswerChecklistCell';
import ChecklistAnswerDateCell from './cell/ChecklistAnswerDateCell';
import ChecklistAnswerDoneBeforeCell from './cell/ChecklistAnswerDoneBeforeCell';
import usePaging from '../../../components/hooks/usePaging';
import NoRowsOverlay from '../../../components/NoRowsOverlay';

const getChecklistAnswerUrl = (checklistAnswerId: number) => `${checklistAnswerId}`;

const getGridModelFromSort = (sort: ChecklistAnswerSort[]): GridSortModel =>
  sort.map((s) => {
    // First check regular field mapping
    const regularField = Object.keys(ChecklistAnswerFieldSortMap).find(
      (key) => ChecklistAnswerFieldSortMap[key as keyof ChecklistAnswerRead] === s.field
    );
    if (regularField) {
      return { field: regularField, sort: s.direction };
    }

    // Then check virtual field mapping
    const virtualField = Object.keys(ChecklistAnswerVirtualFieldSortMap).find(
      (key) => ChecklistAnswerVirtualFieldSortMap[key] === s.field
    );
    if (virtualField) {
      return { field: virtualField, sort: s.direction };
    }

    // Fallback to the field itself
    return { field: s.field, sort: s.direction };
  });

const getSortFromGridModel = (sortModel: GridSortModel): ChecklistAnswerSort[] =>
  sortModel.map((s) => {
    // First check regular field mapping
    const regularFieldSort = ChecklistAnswerFieldSortMap[s.field as keyof ChecklistAnswerRead];
    if (regularFieldSort) {
      return { field: regularFieldSort, direction: s.sort as 'asc' | 'desc' };
    }

    // Then check virtual field mapping
    const virtualFieldSort = ChecklistAnswerVirtualFieldSortMap[s.field];
    if (virtualFieldSort) {
      return { field: virtualFieldSort, direction: s.sort as 'asc' | 'desc' };
    }

    // Fallback to the field itself
    return { field: s.field as ChecklistAnswerSortField, direction: s.sort as 'asc' | 'desc' };
  });

const columnDefaults: Record<ChecklistAnswerColumn, GridColDef<ChecklistAnswerRead>> = {
  [ChecklistAnswerColumn.SID]: {
    field: ChecklistAnswerColumn.SID,
    headerName: ChecklistAnswerColumnDisplayMap[ChecklistAnswerColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistAnswerRead, number, string>) =>
      DataGridCellLinkWrapper(SidCell(params), getChecklistAnswerUrl(params.row.id)),
  },
  [ChecklistAnswerColumn.DESCRIPTION]: {
    field: ChecklistAnswerColumn.DESCRIPTION,
    headerName: ChecklistAnswerColumnDisplayMap[ChecklistAnswerColumn.DESCRIPTION],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistAnswerRead, string, string>) =>
      DataGridCellLinkWrapper(ChecklistAnswerDescriptionCell(params), getChecklistAnswerUrl(params.row.id)),
  },
  [ChecklistAnswerColumn.GROUP]: {
    field: ChecklistAnswerColumn.GROUP,
    headerName: ChecklistAnswerColumnDisplayMap[ChecklistAnswerColumn.GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistAnswerRead, string, string>) =>
      DataGridCellLinkWrapper(GroupCell(params), getChecklistAnswerUrl(params.row.id)),
    valueGetter: (_, row) => row.group?.name || '',
  },
  [ChecklistAnswerColumn.ASSIGNEE]: {
    field: ChecklistAnswerColumn.ASSIGNEE,
    headerName: ChecklistAnswerColumnDisplayMap[ChecklistAnswerColumn.ASSIGNEE],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistAnswerRead, string, string>) =>
      DataGridCellLinkWrapper(UserCell(params), getChecklistAnswerUrl(params.row.id)),
    valueGetter: (_, row) => row.assignee?.fullName || '',
  },
  [ChecklistAnswerColumn.CHECKLIST]: {
    field: ChecklistAnswerColumn.CHECKLIST,
    headerName: ChecklistAnswerColumnDisplayMap[ChecklistAnswerColumn.CHECKLIST],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistAnswerRead>) =>
      DataGridCellLinkWrapper(ChecklistAnswerChecklistCell(params), getChecklistAnswerUrl(params.row.id)),
    valueGetter: (_, row) => row.checklist?.checklistTemplate?.name || '',
  },
  [ChecklistAnswerColumn.CHANGE]: {
    field: ChecklistAnswerColumn.CHANGE,
    headerName: ChecklistAnswerColumnDisplayMap[ChecklistAnswerColumn.CHANGE],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistAnswerRead>) =>
      DataGridCellLinkWrapper(ChecklistAnswerChangeCell(params), getChecklistAnswerUrl(params.row.id)),
    valueGetter: (_, row) => `#${row.change?.sid} ${row.change?.title}`,
  },
  [ChecklistAnswerColumn.DONE_BEFORE]: {
    field: ChecklistAnswerColumn.DONE_BEFORE,
    headerName: ChecklistAnswerColumnDisplayMap[ChecklistAnswerColumn.DONE_BEFORE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistAnswerRead, string, string>) =>
      DataGridCellLinkWrapper(ChecklistAnswerDoneBeforeCell(params), getChecklistAnswerUrl(params.row.id)),
    valueGetter: (_, row) => ChecklistDoneBeforeDisplayMap[row.doneBefore] || '',
  },
  [ChecklistAnswerColumn.STATUS]: {
    field: ChecklistAnswerColumn.STATUS,
    headerName: ChecklistAnswerColumnDisplayMap[ChecklistAnswerColumn.STATUS],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistAnswerRead, string, string>) =>
      DataGridCellLinkWrapper(ChecklistAnswerStatusCell(params), getChecklistAnswerUrl(params.row.id)),
    valueGetter: (_, row) => IssueStatusDisplayMap[row.status] || '',
  },
  [ChecklistAnswerColumn.DATE]: {
    field: ChecklistAnswerColumn.DATE,
    headerName: ChecklistAnswerColumnDisplayMap[ChecklistAnswerColumn.DATE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistAnswerRead, number, string>) =>
      DataGridCellLinkWrapper(ChecklistAnswerDateCell(params), getChecklistAnswerUrl(params.row.id)),
    valueGetter: (_, row) => row.creationDate,
  },
};

function ChecklistAnswerListPage() {
  const { groupId } = useParams();
  const { page, setPage, pageSize, setPageSize } = usePaging();

  const { data: me } = useGetCurrentUserQuery();
  const checklistAnswerViewState = useAppSelector((state) => state.checklistAnswer.checklistAnswerViewState);
  const [sortModel, setSortModel] = useState<GridSortModel>(getGridModelFromSort(checklistAnswerViewState?.sort || []));
  const dispatch = useAppDispatch();

  const columns = useMemo(() => {
    const cols = checklistAnswerViewState.columns ? checklistAnswerViewState.columns : ChecklistAnswerColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [checklistAnswerViewState.columns]);

  const getFilter = (view?: 'open' | 'all') => {
    const usedView = view || checklistAnswerViewState.listView;
    if (usedView === 'all') {
      return `ancestorId=${groupId}`;
    }

    return `
      status=${IssueStatus.ACTIVE}
      &
      ancestorId=${groupId}
    `
      .replace(/\n/g, '')
      .replace(/\s/g, '');
  };

  const [queryParams, setQueryParams] = useState<ChecklistAnswerParams>({
    pageSize,
    pageNumber: page,
    groupId: checklistAnswerViewState.group?.id,
    status: checklistAnswerViewState.status,
    createdBy: checklistAnswerViewState.createdBy?.id,
    search: checklistAnswerViewState.search,
    candidateGroups: checklistAnswerViewState.candidateGroups,
    doneBefore: checklistAnswerViewState.doneBefore,
    assignee: checklistAnswerViewState.assignee?.id,
    filter: getFilter(),
    sort: checklistAnswerViewState.sort?.map(s => `${s.field}:${s.direction}`).join(','),
  });
  const { data, isLoading, error } = useGetChecklistAnswersQuery(queryParams);

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      pageSize,
      pageNumber: page,
      groupId: checklistAnswerViewState.group?.id,
      status: checklistAnswerViewState.status,
      createdBy: checklistAnswerViewState.createdBy?.id,
      search: checklistAnswerViewState.search,
      candidateGroups: checklistAnswerViewState.candidateGroups,
      doneBefore: checklistAnswerViewState.doneBefore,
      assignee: checklistAnswerViewState.assignee?.id,
      sort: checklistAnswerViewState.sort?.map(s => `${s.field}:${s.direction}`).join(','),
    }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checklistAnswerViewState, page, pageSize]);

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      filter: getFilter(checklistAnswerViewState.listView),
    }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checklistAnswerViewState.listView]);

  const onTabSwitch = (view: 'open' | 'all') => {
    setPage(0);
    dispatch(
      setChecklistAnswerViewState({
        ...checklistAnswerViewState,
        listView: view,
      })
    );
  };

  const handlePaginationChange = (updatedModel: GridPaginationModel) => {
    setPageSize(updatedModel.pageSize);
    setPage(updatedModel.page);
  };

  const handleSortModelChange = (newSortModel: GridSortModel) => {
    setSortModel(newSortModel);
    const newSort = getSortFromGridModel(newSortModel);
    dispatch(
      setChecklistAnswerViewState({
        ...checklistAnswerViewState,
        sort: newSort,
      })
    );
  };

  const resetPageNumber = (): void => {
    handlePaginationChange({ page: 0, pageSize });
  };

  return (
    <ErrorGate error={error}>
      <PageTitle page="Issues" />
      <TabContext value="0">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={checklistAnswerViewState.listView} sx={{ width: 'fit-content' }}>
            <Tab label="Open" value="open" onClick={() => onTabSwitch('open')} />
            <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
          </Tabs>
        </Box>
        <ChecklistAnswerChipFilter
          me={me}
          resetPageNumber={resetPageNumber}
          value={checklistAnswerViewState}
          onChange={(v) => dispatch(setChecklistAnswerViewState(v))}
        />
        <ChecklistAnswerFilterBar
          groupId={Number(groupId)}
          resetPageNumber={resetPageNumber}
          value={checklistAnswerViewState}
          onChange={(v) => dispatch(setChecklistAnswerViewState(v))}
        />
        <TabPanel sx={{ px: 0, pt: 1, pb: 2 }} value="0">
          <Paper elevation={4}>
            <Box
              sx={{
                height: 'calc(100vh - 269px)',
                overflow: 'hidden',
                '@media (max-height: 600px)': {
                  height: '100%',
                },
              }}
            >
              <DataGrid
                rows={data?.content || []}
                columns={columns}
                rowCount={data?.total || 0}
                loading={isLoading}
                disableColumnMenu
                pagination
                paginationMode="server"
                paginationModel={{ page, pageSize }}
                onPaginationModelChange={handlePaginationChange}
                sortingMode="server"
                sortModel={sortModel}
                onSortModelChange={handleSortModelChange}
                disableRowSelectionOnClick
                slots={{
                  noRowsOverlay: NoRowsOverlay,
                }}
                onColumnWidthChange={(params) => {
                  const newViewState = { ...checklistAnswerViewState };
                  if (newViewState.columns) {
                    // Clone the columns array to avoid direct mutation.
                    const updatedColumns = [...newViewState.columns];
                    // Find the column to update.
                    const columnToUpdate = updatedColumns.find((c) => c.column === params.colDef.field);
                    if (columnToUpdate) {
                      // Get the index of the column and update immutably.
                      const index = updatedColumns.indexOf(columnToUpdate);
                      updatedColumns[index] = { ...columnToUpdate, width: params.width };
                    }
                    newViewState.columns = updatedColumns;
                  }
                  dispatch(setChecklistAnswerViewState(newViewState));
                }}
                slotProps={{
                  loadingOverlay: { variant: 'skeleton', noRowsVariant: 'skeleton' },
                  noRowsOverlay: { title: 'No issues found' },
                }}
              />
            </Box>
          </Paper>
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}

export default ChecklistAnswerListPage;
