import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import { ChecklistAnswerRead } from '../checklistAnswerTypes';
import Cell from '../../../../components/Cell';
import CellText from '../../../../components/CellText';

type ChecklistAnswerDateCellParam = GridRenderCellParams<ChecklistAnswerRead, number>;

export default function ChecklistAnswerDateCell(params: ChecklistAnswerDateCellParam) {
  const { formattedValue: date } = params;

  if (!date) {
    return null;
  }

  const displayText = new Date(date).toDateString();

  return (
    <Cell title={displayText}>
      <DateRangeIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} /> <CellText>{displayText}</CellText>
    </Cell>
  );
}
