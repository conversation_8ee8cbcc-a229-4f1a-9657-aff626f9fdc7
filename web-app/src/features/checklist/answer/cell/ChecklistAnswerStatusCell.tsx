import { GridRenderCellParams } from '@mui/x-data-grid';
import LockIcon from '@mui/icons-material/Lock';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import { ChecklistAnswerRead } from '../checklistAnswerTypes';
import Cell from '../../../../components/Cell';
import CellText from '../../../../components/CellText';

type ChecklistAnswerStatusCellParam = GridRenderCellParams<ChecklistAnswerRead, string>;

export default function ChecklistAnswerStatusCell(params: ChecklistAnswerStatusCellParam) {
  const { row, formattedValue: statusText } = params;
  const { status, locked } = row;

  if (!status || !statusText) {
    return null;
  }

  return (
    <Cell title={statusText}>
      {locked ? <LockIcon fontSize="small" /> : <LockOpenIcon fontSize="small" />} <CellText>{statusText}</CellText>
    </Cell>
  );
}
