import { GridRenderCellParams } from '@mui/x-data-grid';
import { ChecklistAnswerRead } from '../checklistAnswerTypes';
import CellText from '../../../../components/CellText';
import Cell from '../../../../components/Cell';

type ChecklistAnswerDescriptionCellParam = GridRenderCellParams<ChecklistAnswerRead, string>;

export default function ChecklistAnswerDescriptionCell(params: ChecklistAnswerDescriptionCellParam) {
  const { formattedValue: description } = params;

  if (!description || description.length === 0) {
    return null;
  }

  return (
    <Cell title={description}>
      <CellText>{description}</CellText>
    </Cell>
  );
}
