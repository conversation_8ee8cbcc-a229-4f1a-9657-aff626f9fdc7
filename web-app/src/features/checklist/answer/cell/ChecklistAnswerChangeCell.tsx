import { GridRenderCellParams } from '@mui/x-data-grid';
import SyncIcon from '@mui/icons-material/Sync';
import { Link } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { ChecklistAnswerRead } from '../checklistAnswerTypes';
import Cell from '../../../../components/Cell';
import CellText from '../../../../components/CellText';

type ChecklistAnswerChangeCellParam = GridRenderCellParams<ChecklistAnswerRead>;

export default function ChecklistAnswerChangeCell(params: ChecklistAnswerChangeCellParam) {
  const { row } = params;
  const { change } = row;

  if (!change) {
    return null;
  }

  const displayText = `#${change.sid} ${change.title}`;

  return (
    <Cell title={displayText}>
      <SyncIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />{' '}
      <Link underline="hover" component={RouterLink} to={`./../../changes/${change.id}`}>
        #{change.sid}
      </Link>{' '}
      <CellText>{change.title}</CellText>
    </Cell>
  );
}
