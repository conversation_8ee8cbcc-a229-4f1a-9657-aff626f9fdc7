import { GridRenderCellParams } from '@mui/x-data-grid';
import { ChecklistAnswerRead } from '../checklistAnswerTypes';
import CellText from '../../../../components/CellText';
import Cell from '../../../../components/Cell';

type ChecklistAnswerSidCellParam = GridRenderCellParams<ChecklistAnswerRead, number>;

export default function ChecklistAnswerSidCell(params: ChecklistAnswerSidCellParam) {
  const { formattedValue: sid } = params;

  if (!sid) {
    return null;
  }

  return (
    <Cell title={sid.toString()}>
      <CellText>{sid}</CellText>
    </Cell>
  );
}
