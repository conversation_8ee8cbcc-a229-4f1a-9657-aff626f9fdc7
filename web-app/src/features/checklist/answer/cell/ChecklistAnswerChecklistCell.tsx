import { GridRenderCellParams } from '@mui/x-data-grid';
import ChecklistIcon from '@mui/icons-material/ChecklistOutlined';
import { ChecklistAnswerRead } from '../checklistAnswerTypes';
import Cell from '../../../../components/Cell';
import CellText from '../../../../components/CellText';

type ChecklistAnswerChecklistCellParam = GridRenderCellParams<ChecklistAnswerRead>;

export default function ChecklistAnswerChecklistCell(params: ChecklistAnswerChecklistCellParam) {
  const { row } = params;
  const { checklist } = row;

  if (!checklist) {
    return null;
  }

  const displayText = checklist.checklistTemplate.name;

  return (
    <Cell title={displayText}>
      <ChecklistIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} /> <CellText>{displayText}</CellText>
    </Cell>
  );
}
