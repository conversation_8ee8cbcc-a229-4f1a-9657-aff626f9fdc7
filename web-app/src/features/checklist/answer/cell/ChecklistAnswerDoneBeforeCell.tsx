import { GridRenderCellParams } from '@mui/x-data-grid';
import { ChecklistAnswerRead } from '../checklistAnswerTypes';
import Cell from '../../../../components/Cell';
import CellText from '../../../../components/CellText';

type ChecklistAnswerDoneBeforeCellParam = GridRenderCellParams<ChecklistAnswerRead, string>;

export default function ChecklistAnswerDoneBeforeCell(params: ChecklistAnswerDoneBeforeCellParam) {
  const { formattedValue: doneBefore } = params;

  if (!doneBefore || doneBefore.length === 0) {
    return null;
  }

  return (
    <Cell title={doneBefore}>
      <CellText>{doneBefore}</CellText>
    </Cell>
  );
}
