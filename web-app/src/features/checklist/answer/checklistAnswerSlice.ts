import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { ChecklistAnswerColumnDefaults, ChecklistAnswerState, ChecklistAnswerViewState } from './checklistAnswerTypes';

const initialState: ChecklistAnswerState = {
  checklistAnswerViewState: {
    listView: 'open',
    columns: ChecklistAnswerColumnDefaults,
  },
  checklistAnswerTabViewState: {
    listView: 'open',
    columns: ChecklistAnswerColumnDefaults,
  },
};

export const checklistAnswerSlice = createSlice({
  name: 'checklistAnswer',
  initialState,
  reducers: {
    setChecklistAnswerViewState: (state, action: PayloadAction<ChecklistAnswerViewState>) => {
      state.checklistAnswerViewState = action.payload;
    },
    setChecklistAnswerTabViewState: (state, action: PayloadAction<ChecklistAnswerViewState>) => {
      state.checklistAnswerTabViewState = action.payload;
    },
  },
});

export const { setChecklistAnswerViewState, setChecklistAnswerTabViewState } = checklistAnswerSlice.actions;

export const checklistAnswerReducer = persistReducer(
  {
    key: 'checklistAnswer',
    storage,
    whitelist: ['checklistAnswerViewState', 'checklistAnswerTabViewState'],
  },
  checklistAnswerSlice.reducer
);
