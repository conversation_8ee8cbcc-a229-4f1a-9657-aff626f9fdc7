import { Tab<PERSON>ontext, TabPanel } from '@mui/lab';
import { Box, Paper, Tab, Tabs } from '@mui/material';
import { DataGrid, GridColDef, GridPaginationModel, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';
import { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useGetChecklistsQuery } from './checklistApi';
import {
  ChecklistColumn,
  ChecklistColumnDefaults,
  ChecklistColumnDisplayMap,
  ChecklistFieldSortMap,
  ChecklistVirtualFieldSortMap,
  ChecklistParams,
  ChecklistRead,
  ChecklistSort,
  ChecklistSortField,
  ChecklistStatus,
  ChecklistStatusDisplayMap,
} from './checklistTypes';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import ChecklistFilterBar from './ChecklistFilterBar';
import ChecklistChipFilter from './ChecklistChipFilter';
import { useGetCurrentUserQuery } from '../user/userApi';
import { useAppDispatch, useAppSelector } from '../../store';
import { setChecklistViewState } from './checklistSlice';
import { DataGridCellLinkWrapper } from '../../components/DataGridCellLink';
import GroupCell from '../../components/GroupCell';
import SidCell from '../../components/SidCell';
import UserCell from '../../components/UserCell';
import ChecklistNameCell from './cell/ChecklistNameCell';
import ChecklistStatusCell from './cell/ChecklistStatusCell';
import ChecklistIssuesCell from './cell/ChecklistIssuesCell';
import ChecklistChangeCell from './cell/ChecklistChangeCell';
import ChecklistSummaryCell from './cell/ChecklistSummaryCell';
import { GroupDisplay } from '../group/groupTypes';
import { UserDisplay } from '../user/userTypes';
import usePaging from '../../components/hooks/usePaging';
import NoRowsOverlay from '../../components/NoRowsOverlay';

const getChecklistUrl = (checklistId: number) => `${checklistId}`;

const getGridModelFromSort = (sort: ChecklistSort[]): GridSortModel =>
  sort.map((s) => {
    // First check regular field mapping
    const regularField = Object.keys(ChecklistFieldSortMap).find(
      (key) => ChecklistFieldSortMap[key as keyof ChecklistRead] === s.field
    );
    if (regularField) {
      return { field: regularField, sort: s.direction };
    }

    // Then check virtual field mapping
    const virtualField = Object.keys(ChecklistVirtualFieldSortMap).find(
      (key) => ChecklistVirtualFieldSortMap[key] === s.field
    );
    if (virtualField) {
      return { field: virtualField, sort: s.direction };
    }

    // Fallback to the field itself
    return { field: s.field, sort: s.direction };
  });

const getSortFromGridModel = (sortModel: GridSortModel): ChecklistSort[] =>
  sortModel.map((s) => {
    // First check regular field mapping
    const regularFieldSort = ChecklistFieldSortMap[s.field as keyof ChecklistRead];
    if (regularFieldSort) {
      return { field: regularFieldSort, direction: s.sort as 'asc' | 'desc' };
    }

    // Then check virtual field mapping
    const virtualFieldSort = ChecklistVirtualFieldSortMap[s.field];
    if (virtualFieldSort) {
      return { field: virtualFieldSort, direction: s.sort as 'asc' | 'desc' };
    }

    // Fallback to the field itself
    return { field: s.field as ChecklistSortField, direction: s.sort as 'asc' | 'desc' };
  });

const columnDefaults: Record<ChecklistColumn, GridColDef<ChecklistRead>> = {
  [ChecklistColumn.SID]: {
    field: ChecklistColumn.SID,
    headerName: ChecklistColumnDisplayMap[ChecklistColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistRead, number, string>) =>
      DataGridCellLinkWrapper(SidCell(params), getChecklistUrl(params.row.id)),
  },
  [ChecklistColumn.TITLE]: {
    field: ChecklistColumn.TITLE,
    headerName: ChecklistColumnDisplayMap[ChecklistColumn.TITLE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistRead, string, string>) =>
      DataGridCellLinkWrapper(ChecklistNameCell(params), getChecklistUrl(params.row.id)),
    valueGetter: (_, row) => row.checklistTemplate.name,
  },
  [ChecklistColumn.GROUP]: {
    field: ChecklistColumn.GROUP,
    headerName: ChecklistColumnDisplayMap[ChecklistColumn.GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistRead, string, string>) =>
      DataGridCellLinkWrapper(GroupCell(params), getChecklistUrl(params.row.id)),
    valueGetter: (value: GroupDisplay) => (value ? value.name : ''),
  },
  [ChecklistColumn.ASSIGNEE]: {
    field: ChecklistColumn.ASSIGNEE,
    headerName: ChecklistColumnDisplayMap[ChecklistColumn.ASSIGNEE],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistRead, string, string>) =>
      DataGridCellLinkWrapper(UserCell(params), getChecklistUrl(params.row.id)),
    valueGetter: (value: UserDisplay) => (value ? value.fullName : ''),
  },
  [ChecklistColumn.CHANGE]: {
    field: ChecklistColumn.CHANGE,
    headerName: ChecklistColumnDisplayMap[ChecklistColumn.CHANGE],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistRead>) =>
      DataGridCellLinkWrapper(ChecklistChangeCell(params), getChecklistUrl(params.row.id)),
    valueGetter: (_, row) => `#${row.change.sid} ${row.change.title}`,
  },
  [ChecklistColumn.SUMMARY]: {
    field: ChecklistColumn.SUMMARY,
    headerName: ChecklistColumnDisplayMap[ChecklistColumn.SUMMARY],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistRead>) =>
      DataGridCellLinkWrapper(ChecklistSummaryCell(params), getChecklistUrl(params.row.id)),
    valueGetter: () => 'Summary',
  },
  [ChecklistColumn.ISSUES]: {
    field: ChecklistColumn.ISSUES,
    headerName: ChecklistColumnDisplayMap[ChecklistColumn.ISSUES],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistRead>) =>
      DataGridCellLinkWrapper(ChecklistIssuesCell(params), getChecklistUrl(params.row.id)),
    valueGetter: (_, row) => `${row.resolvedIssuesCount} / ${row.issuesTotalCount}`,
  },
  [ChecklistColumn.STATUS]: {
    field: ChecklistColumn.STATUS,
    headerName: ChecklistColumnDisplayMap[ChecklistColumn.STATUS],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChecklistRead, string, string>) =>
      DataGridCellLinkWrapper(ChecklistStatusCell(params), getChecklistUrl(params.row.id)),
    valueGetter: (value: ChecklistStatus) => (value ? ChecklistStatusDisplayMap[value] : ''),
  },
};

function ChecklistListPage() {
  const { groupId } = useParams();
  const { page, setPage, pageSize, setPageSize } = usePaging();

  const { data: me } = useGetCurrentUserQuery();
  const checklistViewState = useAppSelector((state) => state.checklist.checklistViewState);
  const [sortModel, setSortModel] = useState<GridSortModel>(getGridModelFromSort(checklistViewState?.sort || []));
  const dispatch = useAppDispatch();

  const columns = useMemo(() => {
    const cols = checklistViewState.columns ? checklistViewState.columns : ChecklistColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [checklistViewState.columns]);

  const getFilter = (view?: 'open' | 'all') => {
    const usedView = view || checklistViewState.listView;
    if (usedView === 'all') {
      return `ancestorGroupId=${groupId}`;
    }

    return `
      (status=${ChecklistStatus.ACTIVE}|status=${ChecklistStatus.TODO})
      &
      ancestorGroupId=${groupId}
    `
      .replace(/\n/g, '')
      .replace(/\s/g, '');
  };

  const [queryParams, setQueryParams] = useState<ChecklistParams>({
    pageSize,
    pageNumber: page,
    groupId: checklistViewState.group?.id,
    status: checklistViewState.status,
    assignee: checklistViewState.assignee?.id,
    createdBy: checklistViewState.createdBy?.id,
    search: checklistViewState.search,
    filter: getFilter(),
  });
  const { data, isLoading, error } = useGetChecklistsQuery(queryParams);

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      pageSize,
      pageNumber: page,
    }));
  }, [page, pageSize]);

  useEffect(() => {
    if (checklistViewState) {
      const sort =
        checklistViewState.sort && checklistViewState.sort.length > 0
          ? checklistViewState.sort.map((s) => `${s.field}:${s.direction}`).join(',')
          : undefined;

      setQueryParams((prev) => ({
        ...prev,
        groupId: checklistViewState.group?.id,
        createdBy: checklistViewState.createdBy?.id,
        assignee: checklistViewState.assignee?.id,
        status: checklistViewState.status,
        search: checklistViewState.search,
        sort,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checklistViewState]);

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      filter: getFilter(checklistViewState.listView),
    }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checklistViewState.listView]);

  const onTabSwitch = (view: 'open' | 'all') => {
    setPage(0);
    dispatch(
      setChecklistViewState({
        ...checklistViewState,
        listView: view,
      })
    );
  };

  const handlePaginationChange = (updatedModel: GridPaginationModel) => {
    setPageSize(updatedModel.pageSize);
    setPage(updatedModel.page);
  };

  const handleSortModelChange = (newSortModel: GridSortModel) => {
    setSortModel(newSortModel);
    const newSort = getSortFromGridModel(newSortModel);
    dispatch(
      setChecklistViewState({
        ...checklistViewState,
        sort: newSort,
      })
    );
  };

  const resetPageNumber = (): void => {
    handlePaginationChange({ page: 0, pageSize });
  };

  return (
    <ErrorGate error={error}>
      <PageTitle page="Checklists" />
      <TabContext value="0">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={checklistViewState.listView} sx={{ width: 'fit-content' }}>
            <Tab label="Open" value="open" onClick={() => onTabSwitch('open')} />
            <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
          </Tabs>
        </Box>
        <ChecklistChipFilter me={me} resetPageNumber={resetPageNumber} />
        <ChecklistFilterBar groupId={Number(groupId)} resetPageNumber={resetPageNumber} />
        <TabPanel
          sx={{
            px: 0,
            pt: 1,
            pb: 0,
          }}
          value="0"
        >
          <Paper elevation={4}>
            <Box
              sx={{
                height: 'calc(100vh - 269px)',
                overflow: 'hidden',
                '@media (max-height: 600px)': {
                  height: '100%',
                },
              }}
            >
              <DataGrid
                rows={data?.content || []}
                columns={columns}
                rowCount={data?.total || 0}
                loading={isLoading}
                disableColumnMenu
                pagination
                paginationMode="server"
                paginationModel={{ page, pageSize }}
                onPaginationModelChange={handlePaginationChange}
                sortingMode="server"
                sortModel={sortModel}
                onSortModelChange={handleSortModelChange}
                disableRowSelectionOnClick
                slots={{
                  noRowsOverlay: NoRowsOverlay,
                }}
                onColumnWidthChange={(params) => {
                  const newViewState = { ...checklistViewState };
                  if (newViewState.columns) {
                    // Clone the columns array to avoid direct mutation.
                    const updatedColumns = [...newViewState.columns];
                    // Find the column to update.
                    const columnToUpdate = updatedColumns.find((c) => c.column === params.colDef.field);
                    if (columnToUpdate) {
                      // Get the index of the column and update immutably.
                      const index = updatedColumns.indexOf(columnToUpdate);
                      updatedColumns[index] = { ...columnToUpdate, width: params.width };
                    }
                    newViewState.columns = updatedColumns;
                  }
                  dispatch(setChecklistViewState(newViewState));
                }}
                slotProps={{
                  loadingOverlay: { variant: 'skeleton', noRowsVariant: 'skeleton' },
                  noRowsOverlay: { title: 'No checklists found' },
                }}
              />
            </Box>
          </Paper>
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}

export default ChecklistListPage;
